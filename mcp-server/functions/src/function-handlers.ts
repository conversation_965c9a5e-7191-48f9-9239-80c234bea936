/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import * as express from "express";
import {onRequest} from "firebase-functions/v2/https";
import {defineSecret} from "firebase-functions/params";
import {Request} from "firebase-functions/lib/common/providers/https";
import {HttpsFunction} from "firebase-functions/v2/https";
import {SecretParam} from "firebase-functions/lib/params/types";
import {McpServer} from "./mcp/mcp-server";
import {Crypto} from "./crypto/crypto";
import {Tunnel} from "./ngrok/tunnel";

const mcpServerSecretKey = defineSecret('PROGRESSOR_STARS_MCP_SECRET_KEY');
const ngrokAuthToken = defineSecret('PROGRESSOR_STARS_NGROK_AUTHTOKEN');

export abstract class FunctionHandler<T> {

    protected readonly name: string;

    protected constructor(name: string) {
        this.name = name;
    }

    /**
     * Registers this handler in `module.exports` passed as a parameter.
     *
     * @param moduleExports `module.exports` for the main Firebase Functions app
     */
    public registerIn(moduleExports: any): void {
        moduleExports[this.name] = this.handle();
    }

    protected abstract handle(): T;
}

/**
 * Handles HTTP requests.
 */
export abstract class HttpHandler extends FunctionHandler<HttpsFunction> {

    private readonly timeout: number;

    /**
     * Creates an instance of this handler, with the timeout for its execution.
     *
     * By default, the timeout is 540 seconds, as per Firebase Functions limitation.
     *
     * @param name the name of the Firebase Function instance to be created upon registration
     * @param timeout the timeout for handling an incoming HTTP request
     */
    constructor(name: string, timeout: number = 540) {
        super(name);
        this.timeout = timeout;
    }

    /**
     * Performs handling of the HTTP request.
     *
     * @param request the original request.
     * @param response the HTTP response which corresponds to the request.
     */
    abstract doHandle(request: Request, response: express.Response): void | Promise<void>

    protected handle() {
        const handler = this;
        return onRequest({
            secrets: [mcpServerSecretKey, ngrokAuthToken, ...this.secrets()],
            timeoutSeconds: this.timeout,
            maxInstances: 5,
            region: "us-central1"
        }, async (request, response) => {
            await handler.onBeforeHandle(request, response)
            return handler.doHandle(request, response);
        })
    }

    async onBeforeHandle(_: Request, __: express.Response): Promise<void> {
        // Do nothing by default.
    }

    protected secrets(): SecretParam[] {
        return [];
    }

    protected mcpSecretKey() {
        return mcpServerSecretKey;
    }

    protected ngrokToken() {
        return ngrokAuthToken;
    }
}

/**
 * Base class for handlers that handle specific Express routes within a single Firebase Function.
 */
export abstract class ExpressRouteHandler {

    protected readonly mcpServer: McpServer;
    protected readonly crypto: Crypto;
    protected readonly tunnel: Tunnel;

    constructor() {
        this.mcpServer = new McpServer(() => mcpServerSecretKey.value());
        this.crypto = new Crypto(() => mcpServerSecretKey.value());
        this.tunnel = new Tunnel(() => ngrokAuthToken.value(), 5002);
    }

    /**
     * Registers this route handler with the Express app.
     *
     * @param app the Express application to register the route with
     */
    abstract registerRoute(app: express.Application): void;
}
