/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {HttpHandler} from "../function-handlers";
import {Request} from "firebase-functions/lib/common/providers/https";
import * as express from "express";

/**
 * Handles health check requests to the server.
 */
export class HealthHandler extends HttpHandler {

    constructor() {
        super("mcpServerHealth");
    }

    doHandle(_: Request, response: express.Response): void {
        const serverInfo = this.mcpServer().serverInfo();
        response.json({
            status: 'healthy',
            server: serverInfo.name,
            version: serverInfo.version,
            timestamp: new Date().toISOString()
        });
    }
}
