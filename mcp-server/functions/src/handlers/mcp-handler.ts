/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {ExpressRouteHandler} from "../function-handlers";
import * as express from "express";
import * as logger from "firebase-functions/logger";
import {JSONRPCRequest} from "@modelcontextprotocol/sdk/types.js";
import {IncomingHttpHeaders} from "http";

/**
 * Handles the requests from LLMs according to the MCP protocol.
 */
export class McpHandler extends ExpressRouteHandler {

    registerRoute(app: express.Application): void {
        app.all("/mcp", async (req, res) => {
            try {
                if (req.method === "GET") {
                    const serverInfo = this.mcpServer.serverInfo();
                    res.json({
                        message: "MCP Server endpoint",
                        protocols: ["Streamable HTTP"],
                        server: serverInfo
                    });
                    return;
                }
                const mcpRequest = req.body as JSONRPCRequest;
                this.populateMetadata(mcpRequest, req.headers);
                const mcpResponse = await this.mcpServer.handleRequest(mcpRequest);
                res.json(mcpResponse);
            } catch (error) {
                logger.error("Error handling the MCP request:", error);
                res.status(500).json({
                    jsonrpc: "2.0",
                    id: null,
                    error: {
                        code: -32603,
                        message: "Internal error",
                        data: error instanceof Error ? error.message : "Unknown error"
                    }
                });
            }
        });
    }

    private populateMetadata(mcpRequest: JSONRPCRequest, headers: IncomingHttpHeaders) {
        const metaHeader = headers["x-meta"] as string;
        if (metaHeader) {
            try {
                if (!mcpRequest.params) {
                    mcpRequest.params = {};
                }
                mcpRequest.params!._meta = JSON.parse(metaHeader);
                logger.info(`Extracted metadata from the header: ${metaHeader}.`);
            } catch (error) {
                logger.error("Failed to parse the 'x-meta' header:", error);
            }
        }
    }
}
