/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {ExpressRouteHandler} from "../function-handlers";
import * as express from "express";

/**
 * Handles health check requests to the server.
 */
export class HealthHandler extends ExpressRouteHandler {

    registerRoute(app: express.Application): void {
        app.get("/health", (_req, res) => {
            const serverInfo = this.mcpServer.serverInfo();
            res.json({
                status: 'healthy',
                server: serverInfo.name,
                version: serverInfo.version,
                timestamp: new Date().toISOString()
            });
        });
    }
}
