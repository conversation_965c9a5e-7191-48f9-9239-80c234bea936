/*
 * Copyright 2025, TeamDev. All rights reserved.
 *
 *  Redistribution and use in source and/or binary forms, with or without
 *  modification, must retain the above copyright notice and the following
 *  disclaimer.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMP<PERSON>IED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

import * as express from "express";
import {onRequest} from "firebase-functions/v2/https";
import {defineSecret} from "firebase-functions/params";
import {Request} from "firebase-functions/lib/common/providers/https";
import {TenantContext} from "./tenant/tenant";
import {Current} from "./context";
import {ProgressorUserRepository} from "./auth/core";
import {TenantRepository} from "./tenant/repositories";
import {HttpsFunction} from "firebase-functions/v2/https";
import {CloudFunction} from "firebase-functions/v2/core";
import {ParamsOf} from "firebase-functions/lib/common/params";
import {FirestoreEvent, onDocumentCreated, QueryDocumentSnapshot} from "firebase-functions/firestore";
import {Message} from "@bufbuild/protobuf";
import {SecretParam} from "firebase-functions/lib/params/types";

const gitHubAppPrivateKey = defineSecret('PROGRESSOR_STARS_GH_APP_PRIVATE_KEY');

export abstract class FunctionHandler<T> {

    protected readonly name: string;

    protected constructor(name: string) {
        this.name = name;
    }

    /**
     * Registers this handler in `module.exports` passed as a parameter.
     *
     * @param moduleExports `module.exports` for the main Firebase Functions app
     */
    public registerIn(moduleExports: any): void {
        moduleExports[this.name] = this.handle();
    }

    protected abstract handle(): T;
}

/**
 * Handles HTTP requests.
 */
export abstract class HttpHandler extends FunctionHandler<HttpsFunction> {

    private readonly timeout: number;

    /**
     * Creates an instance of this handler, with the timeout for its execution.
     *
     * By default, the timeout is 540 seconds, as per Firebase Functions limitation.
     *
     * @param name the name of the Firebase Function instance to be created upon registration
     * @param timeout the timeout for handling an incoming HTTP request
     */
    constructor(name: string, timeout: number = 540) {
        super(name);
        this.timeout = timeout;
    }

    /**
     * Performs handling of the HTTP request.
     *
     * @param request the original request.
     * @param response the HTTP response which corresponds to the request.
     */
    abstract doHandle(request: Request, response: express.Response): void | Promise<void>

    protected handle() {
        const handler = this;
        return onRequest({
            secrets: [gitHubAppPrivateKey, ...this.secrets()],
            timeoutSeconds: this.timeout
        }, async (request, response) => {
            await handler.onBeforeHandle(request, response)
            return handler.doHandle(request, response);
        })
    }

    protected writeMsgBinary(value: Message, response: express.Response) {
        const resultingBuffer = value.toBinary();
        response.setHeader("Content-type", "application/octet-stream")
        response.setHeader("Content-length", resultingBuffer.length)
        response.write(resultingBuffer, "binary");
    }

    async onBeforeHandle(_: Request, __: express.Response): Promise<void> {
        // Do nothing by default.
    }

    protected secrets(): SecretParam[] {
        return [];
    }

    protected ghAppPrivateKey() {
        return gitHubAppPrivateKey;
    }

    protected context(): TenantContext {
        return Current.context
    }

    protected tenants(): TenantRepository {
        return Current.app.tenants();
    }

    protected progressorUsers(): ProgressorUserRepository {
        return Current.app.progressorUsers();
    }
}

/**
 * Invoked upon the creation of a new document denoted by the passed `documentPath`,
 * in current instance of Firestore.
 */
export abstract class OnDocCreatedHandler extends FunctionHandler<CloudFunction<any>> {

    private readonly documentPath: string;

    protected constructor(name: string, documentPath: string) {
        super(name);
        this.documentPath = documentPath;
    }

    /**
     * Performs the required action in response to the creation of the Firestore document.
     *
     * @param event describes the created document.
     */
    abstract doHandle(event: FirestoreEvent<QueryDocumentSnapshot | undefined, ParamsOf<string>>): void | Promise<void>

    protected ghAppPrivateKey() {
        return gitHubAppPrivateKey;
    }

    protected handle() {
        const handler = this;

        return onDocumentCreated({
            document: this.documentPath,
            secrets: [gitHubAppPrivateKey],
        }, async (event) => {
            return handler.doHandle(event);
        })
    }
}