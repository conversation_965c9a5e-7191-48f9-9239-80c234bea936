/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {HttpHandler} from "../function-handlers";
import {Request} from "firebase-functions/lib/common/providers/https";
import * as express from "express";
import * as logger from "firebase-functions/logger";

/**
 * Encrypts a token using the server's secret key and returns the encrypted token.
 * This allows the frontend to get an encrypted token without knowing the secret key.
 */
export class EncryptTokenHandler extends HttpHandler {

    constructor() {
        super("mcpServerEncryptToken");
    }

    async doHandle(request: Request, response: express.Response): Promise<void> {
        try {
            const {token} = request.body;
            if (!token || typeof token !== "string") {
                response.status(400).json({
                    error: "Token is required and must be a string"
                });
                return;
            }
            const encryptedToken = this.crypto().encrypt(token);
            response.json({
                encryptedToken: encryptedToken
            });
        } catch (error) {
            logger.error("Error encrypting token:", error);
            response.status(500).json({
                error: "Failed to encrypt token"
            });
        }
    }
}
