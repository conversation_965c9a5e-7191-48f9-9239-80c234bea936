/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

/**
 * The entry point for the Progressor MCP Server hosted on Firebase Functions.
 *
 * The MCP server enables the Progressor app's integration with generative AI models
 * by handling the requests from LLMs to fetch data from the Progressor Mothership
 * and perform actions on behalf of the user.
 *
 * For more information, see https://modelcontextprotocol.io.
 */

import {FunctionHandler} from "./function-handlers";
import {ServerInfoHandler} from "./handlers/server-info-handler";
import {HealthHandler} from "./handlers/health-handler";
import {EncryptTokenHandler} from "./handlers/encrypt-token-handler";
import {PublicUrlHandler} from "./handlers/public-url-handler";
import {McpHandler} from "./handlers/mcp-handler";

/**
 * Register all MCP server handlers with their respective function names.
 */
[
    new ServerInfoHandler(),
    new HealthHandler(),
    new EncryptTokenHandler(),
    new PublicUrlHandler(),
    new McpHandler()
].forEach((handler: FunctionHandler<any>) => {
    handler.registerIn(exports);
});
