/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {ExpressRouteHandler} from "../function-handlers";
import * as express from "express";

/**
 * Handles requests to the root endpoint, providing general information about the server.
 */
export class ServerInfoHandler extends ExpressRouteHandler {

    registerRoute(app: express.Application): void {
        app.get("/", (_req, res) => {
            const serverInfo = this.mcpServer.serverInfo();
            res.json({
                message: "Progressor MCP Server is running on Firebase Functions",
                server: serverInfo,
                endpoints: {
                    health: "/health",
                    encryptToken: "/encrypt-token",
                    mcp: "/mcp",
                    publicUrl: "/publicUrl"
                }
            });
        });
    }
}
