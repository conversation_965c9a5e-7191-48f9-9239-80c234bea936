/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {ExpressRouteHandler} from "../function-handlers";
import * as express from "express";
import * as logger from "firebase-functions/logger";

/**
 * Returns the public URL of this MCP server.
 * In production mode, returns the deployed Firebase Functions URL.
 * In emulator mode, creates an ngrok tunnel if one doesn't exist.
 */
export class PublicUrlHandler extends ExpressRouteHandler {

    registerRoute(app: express.Application): void {
        app.get("/publicUrl", async (_req, res) => {
            try {
                const url = await this.publicUrl();
                res.json({
                    publicUrl: url
                });
            } catch (error) {
                logger.error("Error handling a '/publicUrl' request:", error);
                res.status(500).json({
                    error: "Failed to get or create public URL.",
                    details: error instanceof Error ? error.message : "Unknown error."
                });
            }
        });
    }

    private async publicUrl(): Promise<string> {
        let baseUrl: string;
        if (this.isRunningInEmulator()) {
            const host = await this.tunnel.publicHost();
            const projectId = process.env.GCLOUD_PROJECT || "progressorhq-stars-td-internal";
            baseUrl = `${host}/${projectId}/us-central1`
        } else {
            baseUrl = this.productionHost();
        }
        return `${baseUrl}/mcp`;
    }

    private isRunningInEmulator(): boolean {
        return process.env.FUNCTIONS_EMULATOR === "true"
            || process.env.NODE_ENV === "development"
            || !!process.env.FIREBASE_CONFIG;
    }

    private productionHost(): string {
        return "https://mcp-hayj7tgzhq-uc.a.run.app";
    }
}
