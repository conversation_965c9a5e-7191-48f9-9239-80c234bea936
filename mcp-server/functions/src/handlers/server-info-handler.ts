/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {HttpHand<PERSON>} from "../function-handlers";
import {Request} from "firebase-functions/lib/common/providers/https";
import * as express from "express";

/**
 * <PERSON>les requests to the root endpoint, providing general information about the server.
 */
export class ServerInfoHandler extends HttpHandler {

    constructor() {
        super("mcpServerInfo");
    }

    doHandle(_: Request, response: express.Response): void {
        const serverInfo = this.mcpServer().serverInfo();
        response.json({
            message: "Progressor MCP Server is running on Firebase Functions",
            server: serverInfo,
            endpoints: {
                health: "/mcp-server/health",
                encryptToken: "/mcp-server/encrypt-token",
                mcp: "/mcp-server/mcp",
                publicUrl: "/mcp-server/public-url"
            }
        });
    }
}
