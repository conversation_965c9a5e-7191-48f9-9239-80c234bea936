/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {Http<PERSON>and<PERSON>} from "../function-handlers";
import {Request} from "firebase-functions/lib/common/providers/https";
import * as express from "express";
import * as logger from "firebase-functions/logger";
import {JSONRPCRequest} from "@modelcontextprotocol/sdk/types.js";
import {IncomingHttpHeaders} from "http";

/**
 * Handles the requests from LLMs according to the MCP protocol.
 */
export class Mcp<PERSON>and<PERSON> extends HttpHandler {

    constructor() {
        super("mcpServerMcp");
    }

    async doHandle(request: Request, response: express.Response): Promise<void> {
        try {
            if (request.method === "GET") {
                const serverInfo = this.mcpServer().serverInfo();
                response.json({
                    message: "MCP Server endpoint",
                    protocols: ["Streamable HTTP"],
                    server: serverInfo
                });
                return;
            }
            const mcpRequest = request.body as JSONRPCRequest;
            this.populateMetadata(mcpRequest, request.headers);
            const mcpResponse = await this.mcpServer().handleRequest(mcpRequest);
            response.json(mcpResponse);
        } catch (error) {
            logger.error("Error handling the MCP request:", error);
            response.status(500).json({
                jsonrpc: "2.0",
                id: null,
                error: {
                    code: -32603,
                    message: "Internal error",
                    data: error instanceof Error ? error.message : "Unknown error"
                }
            });
        }
    }

    private populateMetadata(mcpRequest: JSONRPCRequest, headers: IncomingHttpHeaders) {
        const metaHeader = headers["x-meta"] as string;
        if (metaHeader) {
            try {
                if (!mcpRequest.params) {
                    mcpRequest.params = {};
                }
                mcpRequest.params!._meta = JSON.parse(metaHeader);
                logger.info(`Extracted metadata from the header: ${metaHeader}.`);
            } catch (error) {
                logger.error("Failed to parse the 'x-meta' header:", error);
            }
        }
    }
}
