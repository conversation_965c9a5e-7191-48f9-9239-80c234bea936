/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

/**
 * The entry point for the Progressor MCP Server hosted on Firebase Functions.
 *
 * The MCP server enables the Progressor app's integration with generative AI models
 * by handling the requests from LLMs to fetch data from the Progressor Mothership
 * and perform actions on behalf of the user.
 *
 * For more information, see https://modelcontextprotocol.io.
 */

import {onRequest} from "firebase-functions/https";
import * as logger from "firebase-functions/logger";
import express from "express";
import {McpServer} from "./mcp/mcp-server";
import {JSONRPCRequest} from "@modelcontextprotocol/sdk/types.js";
import {defineSecret} from "firebase-functions/params";
import {IncomingHttpHeaders} from "http";
import {Crypto} from "./crypto/crypto";
import {Tunnel} from "./ngrok/tunnel";

/**
 * The name of the secret that stores the secret key used to encrypt and decrypt
 * the Progressor token sent by an LLM.
 */
const mcpServerSecretKey = defineSecret("PROGRESSOR_STARS_MCP_SECRET_KEY");

/**
 * The name of the secret that stores the ngrok auth token.
 */
const ngrokAuthToken = defineSecret("PROGRESSOR_STARS_NGROK_AUTHTOKEN");

/**
 * Configuration constants for the Firebase Functions deployment.
 */
const region = "us-central1";
const port = 5002;

/**
 * The instance of an MCP server that handles requests from LLMs.
 */
const mcpServer = new McpServer(() => mcpServerSecretKey.value());

/**
 * The Express app that handles requests to the MCP server.
 */
const app = express();
app.use(express.json());

const crypto = new Crypto(() => mcpServerSecretKey.value());
const tunnel = new Tunnel(() => ngrokAuthToken.value(), port);

/**
 * Provides the general information about the server.
 */
app.get("/", (_req, res) => {
    const serverInfo = mcpServer.serverInfo();
    res.json({
        message: "Progressor MCP Server is running on Firebase Functions",
        server: serverInfo,
        endpoints: {
            health: "/health",
            encryptToken: "/encrypt-token",
            mcp: "/mcp",
            publicUrl: "/publicUrl"
        }
    });
});

/**
 * Handles the health check requests to the server.
 */
app.get("/health", (_req, res) => {
    const serverInfo = mcpServer.serverInfo();
    res.json({
        status: 'healthy',
        server: serverInfo.name,
        version: serverInfo.version,
        timestamp: new Date().toISOString()
    });
});

/**
 * Encrypts a token using the server's secret key and returns the encrypted token.
 *
 * This allows the frontend to get an encrypted token without knowing the secret key.
 */
app.post("/encrypt-token", async (req, res) => {
    try {
        const {token} = req.body;
        if (!token || typeof token !== "string") {
            res.status(400).json({
                error: "Token is required and must be a string"
            });
            return;
        }
        const encryptedToken = crypto.encrypt(token);
        res.json({
            encryptedToken: encryptedToken
        });
    } catch (error) {
        logger.error("Error encrypting token:", error);
        res.status(500).json({
            error: "Failed to encrypt token"
        });
    }
});

/**
 * Returns the public URL of this MCP server.
 *
 * In production mode, returns the deployed Firebase Functions URL.
 *
 * In emulator mode, creates an ngrok tunnel if one doesn't exist.
 */
app.get("/publicUrl", async (_req, res) => {
    try {
        const url = await publicUrl();
        res.json({
            publicUrl: url
        });
    } catch (error) {
        logger.error("Error handling a '/publicUrl' request:", error);
        res.status(500).json({
            error: "Failed to get or create public URL.",
            details: error instanceof Error ? error.message : "Unknown error."
        });
    }
});

async function publicUrl(): Promise<string> {
    let baseUrl: string;
    if (isRunningInEmulator()) {
        const host = await tunnel.publicHost();
        const projectId = process.env.GCLOUD_PROJECT || "progressorhq-stars-td-internal";
        baseUrl = `https://${host}/${projectId}/${region}`
    } else {
        baseUrl = productionHost();
    }
    return `https://${baseUrl}/mcp`;
}

function isRunningInEmulator(): boolean {
    return process.env.FUNCTIONS_EMULATOR === "true"
        || process.env.NODE_ENV === "development"
        || !!process.env.FIREBASE_CONFIG;
}

function productionHost(): string {
    return "https://mcp-hayj7tgzhq-uc.a.run.app";
}

/**
 * Handles the requests from LLMs according to the MCP protocol.
 */
app.all("/mcp", async (req, res) => {
    try {
        if (req.method === "GET") {
            const serverInfo = mcpServer.serverInfo();
            res.json({
                message: "MCP Server endpoint",
                protocols: ["Streamable HTTP"],
                server: serverInfo
            });
            return;
        }
        const mcpRequest = req.body as JSONRPCRequest;
        populateMetadata(mcpRequest, req.headers);
        const mcpResponse = await mcpServer.handleRequest(mcpRequest);
        res.json(mcpResponse);
    } catch (error) {
        logger.error("Error handling the MCP request:", error);
        res.status(500).json({
            jsonrpc: "2.0",
            id: null,
            error: {
                code: -32603,
                message: "Internal error",
                data: error instanceof Error ? error.message : "Unknown error"
            }
        });
    }
});

function populateMetadata(mcpRequest: JSONRPCRequest, headers: IncomingHttpHeaders) {
    const metaHeader = headers["x-meta"] as string;
    if (metaHeader) {
        try {
            if (!mcpRequest.params) {
                mcpRequest.params = {};
            }
            mcpRequest.params!._meta = JSON.parse(metaHeader);
            logger.info(`Extracted metadata from the header: ${metaHeader}.`);
        } catch (error) {
            logger.error("Failed to parse the 'x-meta' header:", error);
        }
    }
}

export const mcp = onRequest({
    maxInstances: 5,
    region: region,
    secrets: [mcpServerSecretKey, ngrokAuthToken],
}, app);
